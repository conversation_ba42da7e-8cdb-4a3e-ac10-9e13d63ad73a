#!/bin/bash

# Setup script for configuring Framelink Figma MCP server with Augment Code
# This script provides an alternative to the Node.js setup script for Unix-like systems

set -e

echo "🎨 Framelink Figma MCP Server Setup for Augment Code"
echo ""

# Check if VS Code is installed
if ! command -v code &> /dev/null; then
    echo "⚠️  VS Code not found in PATH. Please ensure VS Code is installed and accessible."
    echo "   You can still continue with manual configuration."
    echo ""
fi

# Get Figma API key
read -p "Enter your Figma API key: " FIGMA_API_KEY
if [ -z "$FIGMA_API_KEY" ]; then
    echo "❌ Figma API key is required"
    exit 1
fi

# Ask for output format preference
read -p "Use JSON output format instead of YAML? (y/N): " USE_JSON
if [[ $USE_JSON =~ ^[Yy] ]]; then
    OUTPUT_FORMAT="--json"
else
    OUTPUT_FORMAT=""
fi

# Ask for server name
read -p "Server name (default: Framelink Figma MCP): " SERVER_NAME
SERVER_NAME=${SERVER_NAME:-"Framelink Figma MCP"}

# Determine VS Code settings path based on OS
case "$(uname -s)" in
    Darwin)
        SETTINGS_PATH="$HOME/Library/Application Support/Code/User/settings.json"
        ;;
    Linux)
        SETTINGS_PATH="$HOME/.config/Code/User/settings.json"
        ;;
    CYGWIN*|MINGW32*|MSYS*|MINGW*)
        SETTINGS_PATH="$HOME/AppData/Roaming/Code/User/settings.json"
        ;;
    *)
        echo "❌ Unsupported operating system"
        exit 1
        ;;
esac

echo ""
echo "📁 VS Code settings path: $SETTINGS_PATH"

# Create settings directory if it doesn't exist
mkdir -p "$(dirname "$SETTINGS_PATH")"

# Create basic settings file if it doesn't exist
if [ ! -f "$SETTINGS_PATH" ]; then
    echo "{}" > "$SETTINGS_PATH"
fi

# Create temporary configuration
TEMP_CONFIG=$(mktemp)
cat > "$TEMP_CONFIG" << EOF
{
  "name": "$SERVER_NAME",
  "command": "npx",
  "args": ["-y", "figma-developer-mcp", "--figma-api-key=$FIGMA_API_KEY", $( [ -n "$OUTPUT_FORMAT" ] && echo "\"$OUTPUT_FORMAT\"," ) "--stdio"]
}
EOF

echo ""
echo "✅ Configuration created:"
cat "$TEMP_CONFIG"
echo ""

echo "📋 To complete the setup:"
echo "1. Copy the configuration above"
echo "2. Open VS Code"
echo "3. Press Cmd/Ctrl + Shift + P"
echo "4. Type 'Augment: Edit Settings' and select it"
echo "5. Under 'Advanced', click 'Edit in settings.json'"
echo "6. Add the configuration to the 'augment.advanced.mcpServers' array"
echo "7. Save and restart VS Code"
echo ""
echo "📖 For detailed instructions, see: docs/augment-code-setup.md"
echo ""
echo "Example settings.json structure:"
echo '{'
echo '  "augment.advanced": {'
echo '    "mcpServers": ['
cat "$TEMP_CONFIG"
echo '    ]'
echo '  }'
echo '}'

# Clean up
rm "$TEMP_CONFIG"

echo ""
echo "🎉 Setup information generated successfully!"
