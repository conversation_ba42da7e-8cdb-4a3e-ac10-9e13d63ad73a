#!/usr/bin/env node

/**
 * Setup script for configuring Framelink Figma MCP server with Augment Code
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function getVSCodeSettingsPath() {
  const platform = process.platform;
  let settingsPath;

  if (platform === 'win32') {
    settingsPath = join(homedir(), 'AppData', 'Roaming', 'Code', 'User', 'settings.json');
  } else if (platform === 'darwin') {
    settingsPath = join(homedir(), 'Library', 'Application Support', 'Code', 'User', 'settings.json');
  } else {
    settingsPath = join(homedir(), '.config', 'Code', 'User', 'settings.json');
  }

  return settingsPath;
}

function loadSettings(settingsPath) {
  if (!existsSync(settingsPath)) {
    return {};
  }

  try {
    const content = readFileSync(settingsPath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.warn(`Warning: Could not parse existing settings.json: ${error.message}`);
    return {};
  }
}

function saveSettings(settingsPath, settings) {
  try {
    writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error(`Error saving settings: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🎨 Framelink Figma MCP Server Setup for Augment Code\n');

  // Get Figma API key
  const apiKey = await question('Enter your Figma API key: ');
  if (!apiKey.trim()) {
    console.error('❌ Figma API key is required');
    process.exit(1);
  }

  // Ask for output format preference
  const useJson = await question('Use JSON output format instead of YAML? (y/N): ');
  const outputFormat = useJson.toLowerCase().startsWith('y') ? '--json' : '';

  // Ask for server name
  const serverName = await question('Server name (default: Framelink Figma MCP): ') || 'Framelink Figma MCP';

  // Get VS Code settings path
  const settingsPath = getVSCodeSettingsPath();
  console.log(`\n📁 VS Code settings path: ${settingsPath}`);

  // Load existing settings
  const settings = loadSettings(settingsPath);

  // Initialize augment.advanced if it doesn't exist
  if (!settings['augment.advanced']) {
    settings['augment.advanced'] = {};
  }

  if (!settings['augment.advanced'].mcpServers) {
    settings['augment.advanced'].mcpServers = [];
  }

  // Check if server already exists
  const existingServerIndex = settings['augment.advanced'].mcpServers.findIndex(
    server => server.name === serverName
  );

  // Create server configuration
  const serverConfig = {
    name: serverName,
    command: 'npx',
    args: ['-y', 'figma-developer-mcp', `--figma-api-key=${apiKey}`, outputFormat, '--stdio'].filter(Boolean)
  };

  if (existingServerIndex >= 0) {
    const update = await question(`Server "${serverName}" already exists. Update it? (Y/n): `);
    if (!update.toLowerCase().startsWith('n')) {
      settings['augment.advanced'].mcpServers[existingServerIndex] = serverConfig;
      console.log('✅ Updated existing server configuration');
    } else {
      console.log('❌ Setup cancelled');
      process.exit(0);
    }
  } else {
    settings['augment.advanced'].mcpServers.push(serverConfig);
    console.log('✅ Added new server configuration');
  }

  // Save settings
  if (saveSettings(settingsPath, settings)) {
    console.log('\n🎉 Setup complete!');
    console.log('\nNext steps:');
    console.log('1. Restart VS Code');
    console.log('2. Open the Augment panel');
    console.log('3. Look for "' + serverName + '" in the MCP servers list');
    console.log('4. Test by pasting a Figma URL in Augment Agent chat');
    console.log('\n📖 For more help, see: docs/augment-code-setup.md');
  } else {
    console.error('\n❌ Failed to save settings. Please configure manually.');
    console.log('\nManual configuration:');
    console.log(JSON.stringify(serverConfig, null, 2));
  }

  rl.close();
}

main().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
