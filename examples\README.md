# Configuration Examples

This directory contains example configuration files for setting up the Framelink Figma MCP server with different editors and scenarios.

## Files

### Augment Code Examples

- **`augment-settings.json`** - Basic Augment Code configuration with API key in command
- **`augment-settings-env.json`** - Augment Code configuration using environment variables
- **`augment-settings-advanced.json`** - Advanced configuration with multiple server instances

### Other Editors

- **`cursor-settings.json`** - Configuration for Cursor editor
- **`vscode-settings.json`** - Configuration for VS Code with other MCP clients

## Usage

1. Choose the appropriate configuration file for your setup
2. Copy the relevant configuration to your editor's settings
3. Replace `YOUR_FIGMA_API_KEY` with your actual Figma API key
4. Restart your editor

## Getting Your Figma API Key

1. Go to [Figma Account Settings](https://www.figma.com/settings)
2. Scroll down to "Personal access tokens"
3. Click "Create new token"
4. Give it a descriptive name
5. Copy the generated token

## Security Note

Never commit your actual API key to version control. Use environment variables or secure configuration management for production deployments.
