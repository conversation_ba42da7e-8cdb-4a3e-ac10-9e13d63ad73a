# Setup Scripts

This directory contains setup scripts to help you configure the Framelink Figma MCP server with Augment Code.

## Available Scripts

### `setup-augment.js` (Recommended)

An interactive Node.js script that automatically configures your VS Code settings for Augment Code.

**Usage:**
```bash
npm run setup:augment
```

**Features:**
- Interactive prompts for configuration
- Automatically detects VS Code settings path
- Backs up existing configuration
- Validates input
- Cross-platform support (Windows, macOS, Linux)

### `setup-augment.sh`

A bash script for Unix-like systems (macOS, Linux) that provides setup guidance.

**Usage:**
```bash
./scripts/setup-augment.sh
```

**Features:**
- Interactive prompts
- Generates configuration examples
- Provides step-by-step instructions
- Works on macOS and Linux

## Quick Setup

The fastest way to get started:

1. **Install the package globally (optional):**
   ```bash
   npm install -g figma-developer-mcp
   ```

2. **Run the setup script:**
   ```bash
   npm run setup:augment
   ```

3. **Follow the prompts:**
   - Enter your Figma API key
   - Choose output format (YAML or JSON)
   - Confirm server name

4. **Restart VS Code**

## Manual Setup

If you prefer manual configuration, see the [Augment Code Setup Guide](../docs/augment-code-setup.md) for detailed instructions.

## Getting Your Figma API Key

1. Go to [Figma Account Settings](https://www.figma.com/settings)
2. Scroll down to "Personal access tokens"
3. Click "Create new token"
4. Give it a descriptive name (e.g., "Augment Code MCP")
5. Copy the generated token

## Troubleshooting

### Script Permissions (Unix/Linux/macOS)

If you get a permission error when running the bash script:

```bash
chmod +x scripts/setup-augment.sh
./scripts/setup-augment.sh
```

### Node.js Not Found

Make sure Node.js is installed and accessible:

```bash
node --version
npm --version
```

If not installed, download from [nodejs.org](https://nodejs.org/).

### VS Code Settings Path Issues

The scripts automatically detect your VS Code settings path. If you're using a different VS Code variant (like VS Code Insiders), you may need to manually configure the settings.

**Common VS Code settings paths:**

- **Windows:** `%APPDATA%\Code\User\settings.json`
- **macOS:** `~/Library/Application Support/Code/User/settings.json`
- **Linux:** `~/.config/Code/User/settings.json`

### Augment Extension Not Found

Make sure you have the Augment extension installed in VS Code:

1. Open VS Code
2. Go to Extensions (Ctrl/Cmd + Shift + X)
3. Search for "Augment"
4. Install the official Augment extension

## Support

If you encounter issues:

1. Check the [troubleshooting section](../docs/augment-code-setup.md#troubleshooting) in the setup guide
2. Join the [Discord community](https://framelink.ai/discord)
3. Open an issue on [GitHub](https://github.com/GLips/Figma-Context-MCP/issues)
