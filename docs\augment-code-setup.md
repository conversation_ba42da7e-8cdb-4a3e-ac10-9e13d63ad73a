# Augment Code Setup Guide

This guide provides detailed instructions for setting up the Framelink Figma MCP server with Augment Code.

## Prerequisites

1. **VS Code** with the Augment extension installed
2. **Node.js** (version 18 or higher)
3. **Figma API Key** - [Get your API key here](https://help.figma.com/hc/en-us/articles/8085703771159-Manage-personal-access-tokens)

## Setup Methods

### Method 1: Augment Settings Panel (Recommended)

This is the easiest method for most users:

1. **Open Augment Settings**
   - Open VS Code
   - Open the Augment panel (usually in the sidebar)
   - Click the gear icon (⚙️) in the upper right corner of the Augment panel

2. **Add MCP Server**
   - In the settings panel, locate the "MCP" section
   - Click the `+` button next to "MCP" to add a new server

3. **Configure the Server**
   - **Name**: `Framelink Figma MCP`
   - **Command**: `npx -y figma-developer-mcp --figma-api-key=YOUR_FIGMA_API_KEY --stdio`
   - Replace `YOUR_FIGMA_API_KEY` with your actual Figma API key

4. **Save and Restart**
   - Save the configuration
   - Restart VS Code to apply the changes

### Method 2: Direct settings.json Configuration

For users who prefer manual configuration:

1. **Access Settings**
   - Press `Cmd/Ctrl + Shift + P` to open the command palette
   - Type "Augment: Edit Settings" and select it
   - OR use the hamburger menu in the Augment panel and select "Edit Settings"

2. **Edit settings.json**
   - Under "Advanced", click "Edit in settings.json"
   - This will open your VS Code settings.json file

3. **Add Configuration**
   Add the following configuration to your settings.json:

   ```json
   {
     "augment.advanced": {
       "mcpServers": [
         {
           "name": "Framelink Figma MCP",
           "command": "npx",
           "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR_FIGMA_API_KEY", "--stdio"]
         }
       ]
     }
   }
   ```

4. **Replace API Key**
   - Replace `YOUR_FIGMA_API_KEY` with your actual Figma API key

5. **Save and Restart**
   - Save the settings.json file
   - Restart VS Code

## Alternative Configuration with Environment Variables

If you prefer to use environment variables instead of passing the API key directly:

### Using .env file

1. Create a `.env` file in your project root:
   ```
   FIGMA_API_KEY=your_figma_api_key_here
   ```

2. Use this configuration in Augment:
   ```json
   {
     "augment.advanced": {
       "mcpServers": [
         {
           "name": "Framelink Figma MCP",
           "command": "npx",
           "args": ["-y", "figma-developer-mcp", "--stdio"],
           "env": {
             "FIGMA_API_KEY": "your_figma_api_key_here"
           }
         }
       ]
     }
   }
   ```

## Verification

After setup, verify the integration is working:

1. **Check Augment Panel**
   - Look for "Framelink Figma MCP" in the Augment panel
   - It should show as "Connected" or similar status

2. **Test with a Figma URL**
   - Open Augment Agent chat
   - Paste a Figma file URL (e.g., `https://www.figma.com/file/ABC123/My-Design`)
   - Ask Augment to analyze the design
   - You should see the MCP server fetch and process the Figma data

## Troubleshooting

### Common Issues

1. **Server Not Starting**
   - Ensure Node.js is installed and accessible
   - Check that your Figma API key is valid
   - Verify the command syntax in your configuration

2. **Permission Errors**
   - On Windows, you might need to run VS Code as administrator
   - Ensure npx has permission to install packages

3. **API Key Issues**
   - Verify your Figma API key is correct
   - Check that the API key has the necessary permissions
   - Ensure there are no extra spaces or characters in the key

4. **Network Issues**
   - Check your internet connection
   - Verify that your firewall isn't blocking the connection
   - Some corporate networks may block npm/npx

### Getting Help

If you encounter issues:

1. Check the [Framelink documentation](https://www.framelink.ai/docs)
2. Join the [Discord community](https://framelink.ai/discord)
3. Open an issue on the [GitHub repository](https://github.com/GLips/Figma-Context-MCP)

## Advanced Configuration

### Custom Port

To run the server on a custom port:

```json
{
  "name": "Framelink Figma MCP",
  "command": "npx",
  "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--port=3333", "--stdio"]
}
```

### JSON Output Format

To use JSON instead of YAML output:

```json
{
  "name": "Framelink Figma MCP",
  "command": "npx",
  "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--json", "--stdio"]
}
```

### Development Mode

For development or debugging:

```json
{
  "name": "Framelink Figma MCP (Dev)",
  "command": "npx",
  "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR_KEY", "--stdio"],
  "env": {
    "NODE_ENV": "development"
  }
}
```
